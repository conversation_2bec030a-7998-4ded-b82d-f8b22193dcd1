# PostgreSQL Checkpointing for LangGraph

This document explains how to set up and use PostgreSQL-based checkpointing for persistent conversation memory in production.

## Overview

LangGraph supports different checkpointing backends:
- **MemorySaver**: In-memory (development only)
- **PostgresSaver**: PostgreSQL-based (production recommended)

## Quick Setup

### Option 1: Using Docker (Recommended for Development)

1. **Start PostgreSQL container:**
```bash
docker-compose -f docker-compose.postgres.yml up -d
```

2. **Configure environment:**
```bash
# Copy example environment file
cp .env.example .env

# Edit .env and set:
POSTGRES_CHECKPOINT_URL=postgresql://langgraph_user:langgraph_password@localhost:5432/langgraph_checkpoints
```

3. **Initialize database:**
```bash
python scripts/setup_postgres_checkpoints.py
```

### Option 2: Using Existing PostgreSQL

1. **Configure environment:**
```bash
# Set your PostgreSQL connection URL
POSTGRES_CHECKPOINT_URL=*********************************************/your_database
```

2. **Initialize database:**
```bash
python scripts/setup_postgres_checkpoints.py
```

## Environment Configuration

### Development
```env
# Use in-memory checkpointing (default)
# POSTGRES_CHECKPOINT_URL=  # Leave unset
```

### Production
```env
# Use PostgreSQL checkpointing
POSTGRES_CHECKPOINT_URL=postgresql://username:password@host:port/database
```

## Connection URL Formats

### Local PostgreSQL
```
postgresql://username:password@localhost:5432/database_name
```

### Cloud PostgreSQL (AWS RDS, Google Cloud SQL, Azure)
```
postgresql://username:password@host:port/database?sslmode=require
```

### With Connection Pooling
```
postgresql://username:password@host:port/database?sslmode=require&pool_size=20&max_overflow=0
```

## Benefits of PostgreSQL Checkpointing

### 🚀 **Production Ready**
- Persistent storage across application restarts
- Handles high concurrency and load
- ACID compliance for data integrity

### 📈 **Scalability**
- Supports multiple application instances
- Horizontal scaling with read replicas
- Connection pooling support

### 🔒 **Reliability**
- Automatic backups and point-in-time recovery
- High availability configurations
- Monitoring and alerting capabilities

### 🔍 **Observability**
- Query performance insights
- Connection monitoring
- Storage usage tracking

## Database Schema

LangGraph automatically creates these tables:

```sql
-- Checkpoint storage
CREATE TABLE checkpoints (
    thread_id TEXT NOT NULL,
    checkpoint_id TEXT NOT NULL,
    parent_checkpoint_id TEXT,
    checkpoint BYTEA NOT NULL,
    metadata BYTEA NOT NULL,
    PRIMARY KEY (thread_id, checkpoint_id)
);

-- Checkpoint writes (for atomic operations)
CREATE TABLE checkpoint_writes (
    thread_id TEXT NOT NULL,
    checkpoint_id TEXT NOT NULL,
    task_id TEXT NOT NULL,
    idx INTEGER NOT NULL,
    channel TEXT NOT NULL,
    value BYTEA,
    PRIMARY KEY (thread_id, checkpoint_id, task_id, idx)
);
```

## Monitoring and Maintenance

### Check Database Size
```sql
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE tablename IN ('checkpoints', 'checkpoint_writes');
```

### Clean Old Checkpoints
```sql
-- Delete checkpoints older than 30 days
DELETE FROM checkpoints 
WHERE checkpoint_id < (
    SELECT checkpoint_id 
    FROM checkpoints 
    WHERE checkpoint_id::timestamp < NOW() - INTERVAL '30 days'
    LIMIT 1
);
```

### Monitor Connections
```sql
SELECT 
    count(*) as active_connections,
    state,
    application_name
FROM pg_stat_activity 
WHERE datname = 'langgraph_checkpoints'
GROUP BY state, application_name;
```

## Troubleshooting

### Connection Issues
```bash
# Test connection
psql "postgresql://username:password@host:port/database" -c "SELECT 1;"
```

### Permission Issues
```sql
-- Grant necessary permissions
GRANT ALL PRIVILEGES ON DATABASE langgraph_checkpoints TO username;
GRANT ALL ON SCHEMA public TO username;
```

### Performance Issues
```sql
-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_checkpoints_thread_id ON checkpoints(thread_id);
CREATE INDEX IF NOT EXISTS idx_checkpoint_writes_thread_id ON checkpoint_writes(thread_id);
```

## Migration from In-Memory

1. **Set up PostgreSQL** following the setup guide above
2. **Configure environment** with `POSTGRES_CHECKPOINT_URL`
3. **Restart application** - existing conversations will start fresh
4. **Verify setup** by checking database tables

## Best Practices

### Security
- Use strong passwords and SSL connections
- Limit database user permissions
- Regular security updates

### Performance
- Use connection pooling
- Monitor query performance
- Regular VACUUM and ANALYZE

### Backup
- Automated daily backups
- Test restore procedures
- Point-in-time recovery setup

### Monitoring
- Set up alerts for connection limits
- Monitor disk space usage
- Track query performance metrics

## Support

For issues with PostgreSQL checkpointing:
1. Check the application logs
2. Verify database connectivity
3. Review the troubleshooting section above
4. Check LangGraph documentation: https://langchain-ai.github.io/langgraph/
