"""
OAuth 2.0 configuration for multiple providers
Supports Google, GitHub, Microsoft, and other OAuth providers
"""
import os
from typing import Dict, Any, Optional
from authlib.integrations.starlette_client import OAuth
from authlib.integrations.starlette_client.apps import StarletteOAuth2App
from pydantic import BaseModel
import secrets

class OAuthProvider(BaseModel):
    """OAuth provider configuration"""
    name: str
    client_id: str
    client_secret: str
    server_metadata_url: Optional[str] = None
    authorize_url: Optional[str] = None
    access_token_url: Optional[str] = None
    userinfo_endpoint: Optional[str] = None
    scopes: list[str] = []
    
class OAuthConfig:
    """OAuth configuration manager"""
    
    def __init__(self):
        self.oauth = OAuth()
        self.providers: Dict[str, StarletteOAuth2App] = {}
        self.session_secret = os.getenv("SESSION_SECRET_KEY", secrets.token_urlsafe(32))
        self._setup_providers()
    
    def _setup_providers(self):
        """Setup OAuth providers based on environment variables"""
        
        # Google OAuth
        google_client_id = os.getenv("GOOGLE_CLIENT_ID")
        google_client_secret = os.getenv("GOOGLE_CLIENT_SECRET")
        if google_client_id and google_client_secret:
            self.providers['google'] = self.oauth.register(
                name='google',
                client_id=google_client_id,
                client_secret=google_client_secret,
                server_metadata_url='https://accounts.google.com/.well-known/openid_configuration',
                client_kwargs={
                    'scope': 'openid email profile'
                }
            )
        
        # GitHub OAuth
        github_client_id = os.getenv("GITHUB_CLIENT_ID")
        github_client_secret = os.getenv("GITHUB_CLIENT_SECRET")
        if github_client_id and github_client_secret:
            self.providers['github'] = self.oauth.register(
                name='github',
                client_id=github_client_id,
                client_secret=github_client_secret,
                access_token_url='https://github.com/login/oauth/access_token',
                authorize_url='https://github.com/login/oauth/authorize',
                api_base_url='https://api.github.com/',
                client_kwargs={'scope': 'user:email'},
            )
        
        # Microsoft OAuth
        microsoft_client_id = os.getenv("MICROSOFT_CLIENT_ID")
        microsoft_client_secret = os.getenv("MICROSOFT_CLIENT_SECRET")
        if microsoft_client_id and microsoft_client_secret:
            tenant_id = os.getenv("MICROSOFT_TENANT_ID", "common")
            self.providers['microsoft'] = self.oauth.register(
                name='microsoft',
                client_id=microsoft_client_id,
                client_secret=microsoft_client_secret,
                access_token_url=f'https://login.microsoftonline.com/{tenant_id}/oauth2/v2.0/token',
                authorize_url=f'https://login.microsoftonline.com/{tenant_id}/oauth2/v2.0/authorize',
                api_base_url='https://graph.microsoft.com/',
                client_kwargs={'scope': 'openid email profile'},
            )
        
        # Discord OAuth
        discord_client_id = os.getenv("DISCORD_CLIENT_ID")
        discord_client_secret = os.getenv("DISCORD_CLIENT_SECRET")
        if discord_client_id and discord_client_secret:
            self.providers['discord'] = self.oauth.register(
                name='discord',
                client_id=discord_client_id,
                client_secret=discord_client_secret,
                access_token_url='https://discord.com/api/oauth2/token',
                authorize_url='https://discord.com/api/oauth2/authorize',
                api_base_url='https://discord.com/api/',
                client_kwargs={'scope': 'identify email'},
            )
    
    def get_provider(self, provider_name: str) -> Optional[StarletteOAuth2App]:
        """Get OAuth provider by name"""
        return self.providers.get(provider_name)
    
    def get_available_providers(self) -> list[str]:
        """Get list of available OAuth providers"""
        return list(self.providers.keys())
    
    def is_provider_configured(self, provider_name: str) -> bool:
        """Check if a provider is configured"""
        return provider_name in self.providers

# Global OAuth configuration instance
oauth_config = OAuthConfig()

def get_oauth_config() -> OAuthConfig:
    """Get the global OAuth configuration"""
    return oauth_config
