"""
Simple login authentication with JWT tokens
Pure /login endpoint with username/password authentication
"""
from datetime import datetime, timed<PERSON>ta
from typing import Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Depends, status, Request
from fastapi.security import <PERSON>A<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OAuth2PasswordRequestForm
from passlib.context import CryptContext
from pydantic import BaseModel, EmailStr
from jose import JWTError, jwt
import os

from src.models.user import UserService, UserCreate, UserResponse, UserInDB, CurrentUser, TenantInfo
from src.config.database import get_collection, COLLECTIONS

router = APIRouter()

# OAuth2 scheme for Swagger UI
oauth2_scheme = OAuth2AuthorizationCodeBearer(
    authorizationUrl="/api/auth/login/google",
    tokenUrl="/api/auth/token",
    auto_error=False
)

# OAuth provider configurations
OAUTH_PROVIDERS = {
    "google": {
        "client_id": os.getenv("GOOGLE_CLIENT_ID"),
        "client_secret": os.getenv("GOOGLE_CLIENT_SECRET"),
        "auth_url": "https://accounts.google.com/o/oauth2/auth",
        "token_url": "https://oauth2.googleapis.com/token",
        "user_info_url": "https://www.googleapis.com/oauth2/v2/userinfo",
        "scope": "openid email profile"
    },
    "github": {
        "client_id": os.getenv("GITHUB_CLIENT_ID"),
        "client_secret": os.getenv("GITHUB_CLIENT_SECRET"),
        "auth_url": "https://github.com/login/oauth/authorize",
        "token_url": "https://github.com/login/oauth/access_token",
        "user_info_url": "https://api.github.com/user",
        "scope": "user:email"
    }
}

# Response models
class OAuthProvidersResponse(BaseModel):
    """Available OAuth providers response"""
    providers: list[str]
    login_urls: Dict[str, str]

class LoginResponse(BaseModel):
    """Login response"""
    success: bool
    message: str
    user: Optional[UserResponse] = None
    redirect_url: Optional[str] = None

# Utility functions
async def get_user_service():
    """Get user service instance"""
    users_collection = await get_collection(COLLECTIONS['users'])
    return UserService(users_collection)

async def get_oauth_user_info(provider: str, access_token: str) -> Dict[str, Any]:
    """Get user info from OAuth provider"""
    config = OAUTH_PROVIDERS.get(provider)
    if not config:
        raise HTTPException(status_code=400, detail=f"Provider {provider} not supported")

    headers = {"Authorization": f"Bearer {access_token}"}

    async with httpx.AsyncClient() as client:
        if provider == "github":
            # GitHub needs special handling for email
            user_resp = await client.get(config["user_info_url"], headers=headers)
            user_data = user_resp.json()

            # Get email separately if not public
            if not user_data.get("email"):
                email_resp = await client.get("https://api.github.com/user/emails", headers=headers)
                emails = email_resp.json()
                primary_email = next((e["email"] for e in emails if e["primary"]), None)
                if primary_email:
                    user_data["email"] = primary_email
        else:
            user_resp = await client.get(config["user_info_url"], headers=headers)
            user_data = user_resp.json()

    return user_data

async def create_or_update_user_from_oauth(provider: str, user_data: Dict[str, Any]) -> UserInDB:
    """Create or update user from OAuth information"""
    user_service = await get_user_service()

    # Extract email and name based on provider
    if provider == "google":
        email = user_data.get("email")
        name = user_data.get("name", "")
    elif provider == "github":
        email = user_data.get("email")
        name = user_data.get("name") or user_data.get("login", "")
    else:
        email = user_data.get("email")
        name = user_data.get("name", "")

    if not email:
        raise HTTPException(status_code=400, detail="Email not provided by OAuth provider")

    # Try to find existing user by email
    existing_user = await user_service.get_user_by_email(email)

    if existing_user:
        # Update existing user with OAuth info
        await user_service.update_last_login(str(existing_user.id))
        return existing_user
    else:
        # Create new user from OAuth data
        import uuid

        # Generate a random password (won't be used for OAuth users)
        random_password = secrets.token_urlsafe(32)

        # Create default tenant info
        tenant_info = TenantInfo(
            tenant_id=str(uuid.uuid4()),
            tenant_name=f"{name}'s Workspace",
            tenant_type="individual",
            subscription_plan="free",
            max_conversations=100,
            max_messages_per_day=1000,
            features=["basic_chat", "conversation_history", "oauth_login"]
        )

        user_create_data = UserCreate(
            name=name,
            email=email,
            phone="",  # OAuth providers might not provide phone
            password=random_password,  # Won't be used
            tenant_info=tenant_info
        )

        return await user_service.create_user(user_create_data)

def get_current_user_from_session(request: Request) -> Optional[CurrentUser]:
    """Get current user from session"""
    user_data = request.session.get('user')
    if user_data:
        # Reconstruct user from session data
        user_in_db = UserInDB(**user_data)
        return CurrentUser(user_in_db)
    return None

async def get_current_user(request: Request) -> CurrentUser:
    """Get current authenticated user from session"""
    current_user = get_current_user_from_session(request)
    if not current_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated. Please login with OAuth.",
        )
    return current_user

# Routes
@router.get("/providers", response_model=OAuthProvidersResponse)
async def get_oauth_providers(request: Request):
    """Get available OAuth providers and their login URLs"""
    base_url = str(request.base_url).rstrip('/')

    # Only include providers that are configured
    available_providers = []
    login_urls = {}

    for provider, config in OAUTH_PROVIDERS.items():
        if config["client_id"] and config["client_secret"]:
            available_providers.append(provider)
            login_urls[provider] = f"{base_url}/api/auth/login/{provider}"

    return OAuthProvidersResponse(
        providers=available_providers,
        login_urls=login_urls
    )

@router.get("/login/{provider}")
async def oauth_login(provider: str, request: Request):
    """Initiate OAuth login with specified provider"""
    config = OAUTH_PROVIDERS.get(provider)
    if not config or not config["client_id"] or not config["client_secret"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"OAuth provider '{provider}' not configured"
        )

    # Generate state for CSRF protection
    state = secrets.token_urlsafe(32)
    request.session['oauth_state'] = state
    request.session['oauth_provider'] = provider

    # Build redirect URI
    base_url = str(request.base_url).rstrip('/')
    redirect_uri = f"{base_url}/api/auth/callback/{provider}"

    # Build authorization URL
    auth_params = {
        "client_id": config["client_id"],
        "redirect_uri": redirect_uri,
        "scope": config["scope"],
        "response_type": "code",
        "state": state
    }

    auth_url = f"{config['auth_url']}?{urlencode(auth_params)}"
    return RedirectResponse(url=auth_url)

@router.get("/callback/{provider}")
async def oauth_callback(provider: str, request: Request):
    """Handle OAuth callback from provider"""
    config = OAUTH_PROVIDERS.get(provider)
    if not config:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"OAuth provider '{provider}' not configured"
        )

    # Verify state for CSRF protection
    state = request.query_params.get('state')
    session_state = request.session.get('oauth_state')
    session_provider = request.session.get('oauth_provider')

    if not state or state != session_state or provider != session_provider:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid OAuth state or provider mismatch"
        )

    # Get authorization code
    code = request.query_params.get('code')
    if not code:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Authorization code not provided"
        )

    try:
        # Exchange code for access token
        base_url = str(request.base_url).rstrip('/')
        redirect_uri = f"{base_url}/api/auth/callback/{provider}"

        token_data = {
            "client_id": config["client_id"],
            "client_secret": config["client_secret"],
            "code": code,
            "grant_type": "authorization_code",
            "redirect_uri": redirect_uri
        }

        async with httpx.AsyncClient() as client:
            token_response = await client.post(
                config["token_url"],
                data=token_data,
                headers={"Accept": "application/json"}
            )
            token_json = token_response.json()
            access_token = token_json.get("access_token")

            if not access_token:
                raise HTTPException(status_code=400, detail="Failed to get access token")

        # Get user info
        user_data = await get_oauth_user_info(provider, access_token)

        # Create or update user
        user = await create_or_update_user_from_oauth(provider, user_data)

        # Store user in session
        request.session['user'] = user.model_dump()
        request.session['authenticated'] = True
        request.session['auth_provider'] = provider

        # Clean up OAuth state
        request.session.pop('oauth_state', None)
        request.session.pop('oauth_provider', None)

        # Redirect to frontend success page
        frontend_url = os.getenv("FRONTEND_URL", "http://localhost:3000")
        return RedirectResponse(url=f"{frontend_url}/auth/success")

    except Exception as e:
        # Clean up session on error
        request.session.pop('oauth_state', None)
        request.session.pop('oauth_provider', None)

        # Redirect to frontend error page
        frontend_url = os.getenv("FRONTEND_URL", "http://localhost:3000")
        error_params = urlencode({"error": str(e)})
        return RedirectResponse(url=f"{frontend_url}/auth/error?{error_params}")

@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: CurrentUser = Depends(get_current_user)):
    """Get current user information"""
    return current_user.to_response()

@router.get("/check")
async def check_auth(request: Request):
    """Check if user is authenticated"""
    current_user = get_current_user_from_session(request)
    
    if not current_user:
        return {
            "authenticated": False,
            "user": None,
            "auth_provider": None
        }
    
    return {
        "authenticated": True,
        "user": current_user.to_response(),
        "auth_provider": request.session.get('auth_provider'),
        "tenant_info": {
            "tenant_id": current_user.tenant_id,
            "subscription_plan": current_user.subscription_plan,
            "max_conversations": current_user.max_conversations,
            "max_messages_per_day": current_user.max_messages_per_day,
            "conversations_count": await current_user.get_user_conversations_count(),
            "messages_today_count": await current_user.get_user_messages_today_count(),
            "can_create_conversation": await current_user.can_create_conversation(),
            "can_send_message": await current_user.can_send_message()
        }
    }

@router.post("/logout")
async def logout(request: Request):
    """Logout user by clearing session"""
    request.session.clear()
    return {"success": True, "message": "Logged out successfully"}
