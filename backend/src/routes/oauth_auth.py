# src/core/security.py

from datetime import datetime, timedelta, timezone
from passlib.context import CryptContext
from fastapi.security import OAuth2PasswordBearer
from fastapi import Depends, HTTPException

import jwt
from src.core.config import SECRET_KEY, ALGORITHM
from src.models.user import UserTenantDB, User
from src.core.database import get_async_db_from_tenant_id, get_db_from_tenant_id
from typing import Optional, Dict
from bson import ObjectId
from src.core.database import get_admin_db

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl="/login",
    auto_error=False  # Don't automatically return 401, let us handle it
)

def create_access_token(data: dict, expires_delta:timedelta = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(days=1)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt






async def get_tenant_info(token: str = Depends(oauth2_scheme)) -> UserTenantDB:
    credentials_exception = HTTPException(
        status_code=401,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    # Check if token is provided (since auto_error=False)
    if not token:
        raise credentials_exception

    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except jwt.PyJWTError:
        raise credentials_exception

    tenant_db = get_db_from_tenant_id(payload.get("tenant_id"))
    async_tenant_db = get_async_db_from_tenant_id(payload.get("tenant_id"))
    user = tenant_db.users.find_one({"username": username})
    if user is None:
        raise credentials_exception
    tenant_database_name = get_admin_db().tenants.find_one({"_id": ObjectId(payload.get("tenant_id"))})["slug"]
    slug = get_admin_db().tenants.find_one({"_id": ObjectId(payload.get("tenant_id"))})["slug"]
    user_role = user.get("role")
    user_access = tenant_db.settings.find_one({"name": "nav_access"})
    role_access = user_access.get(user_role, {}) if user_access else {}
    return UserTenantDB(
        tenant_id=payload.get("tenant_id"),
        tenant_database_name=tenant_database_name, 
        slug=slug,
        db=tenant_db, 
        user=User(**user),
        async_db=async_tenant_db, 
        access=role_access
    )

def require_user(users: list):
    async def check_username(user_tenant_info: UserTenantDB = Depends(get_tenant_info)):
        user_name = user_tenant_info.user.username
        if user_name not in users:
            raise HTTPException(status_code=403, detail="You are not authorized to carry out this action")
        return user_tenant_info
    return check_username

def min_role(min_role: str):
    async def check_role(user_tenant_info: UserTenantDB = Depends(get_tenant_info)):
        user_role = user_tenant_info.user.role
        ROLES = user_tenant_info.db.settings.find_one({"name":"role_hierarchy"})['roles']
        if ROLES[user_role] < ROLES[min_role]:
            raise HTTPException(status_code=403, detail="You are not authorized to carry out this action")
        return user_tenant_info
    return check_role

def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)

def create_invitation_token(username: str, role: str, invited_by: str, tenant_id:str, slug:str, expires_delta: Optional[timedelta] = None):
    """
    Creates a JWT token for agent invitation.
    """
    to_encode = {"username": username, "invited_by": invited_by, "role": role, "tenant_id": tenant_id, "slug": slug}
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(days=7)  # Default expiration: 7 days
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_invitation_token(token: str):
    """
    Verifies the invitation token and extracts the agent's name.
    """
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("username")
        tenant_id: str = payload.get("tenant_id")
        role: str = payload.get("role")
        invited_by: str = payload.get("invited_by")
        result = get_db_from_tenant_id(payload.get("tenant_id")).invitations.find_one({"username": username, "role": role})

        if username is None or tenant_id is None:
            raise HTTPException(status_code=400, detail="Invalid invitation token")

        if result is None:
            raise HTTPException(status_code=400, detail="Invalid invitation token")

        return username, tenant_id,invited_by, role

    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=400, detail="Invitation token has expired")
    except jwt.PyJWTError:
        raise HTTPException(status_code=400, detail="Invalid invitation token")


def verify_password(plain_password, hashed_password):
    """
    Verifies a plain password against a hashed password.
    """
    return pwd_context.verify(plain_password, hashed_password)


