"""
OAuth 2.0 authentication routes
Replaces token-based authentication with OAuth login flow
"""
from datetime import datetime, timed<PERSON>ta
from typing import Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Depends, status, Request, Response
from fastapi.responses import RedirectResponse, JSONResponse
from pydantic import BaseModel, EmailStr
from starlette.middleware.sessions import SessionMiddleware
import secrets
import os
from urllib.parse import urlencode

from src.models.user import UserService, UserCreate, UserResponse, UserInDB, CurrentUser, TenantInfo
from src.config.database import get_collection, COLLECTIONS
from src.config.oauth_config import get_oauth_config, OAuthConfig

router = APIRouter()

# Session settings
SESSION_SECRET_KEY = os.getenv("SESSION_SECRET_KEY", secrets.token_urlsafe(32))

# Response models
class OAuthProvidersResponse(BaseModel):
    """Available OAuth providers response"""
    providers: list[str]
    login_urls: Dict[str, str]

class OAuthUserInfo(BaseModel):
    """OAuth user information"""
    provider: str
    provider_id: str
    email: str
    name: str
    avatar_url: Optional[str] = None
    raw_data: Dict[str, Any] = {}

class LoginResponse(BaseModel):
    """Login response"""
    success: bool
    message: str
    user: Optional[UserResponse] = None
    redirect_url: Optional[str] = None

# Utility functions
async def get_user_service():
    """Get user service instance"""
    users_collection = await get_collection(COLLECTIONS['users'])
    return UserService(users_collection)

def extract_user_info_from_oauth(provider: str, user_data: Dict[str, Any]) -> OAuthUserInfo:
    """Extract user information from OAuth provider response"""
    
    if provider == 'google':
        return OAuthUserInfo(
            provider=provider,
            provider_id=user_data.get('sub', ''),
            email=user_data.get('email', ''),
            name=user_data.get('name', ''),
            avatar_url=user_data.get('picture'),
            raw_data=user_data
        )
    
    elif provider == 'github':
        return OAuthUserInfo(
            provider=provider,
            provider_id=str(user_data.get('id', '')),
            email=user_data.get('email', ''),
            name=user_data.get('name', '') or user_data.get('login', ''),
            avatar_url=user_data.get('avatar_url'),
            raw_data=user_data
        )
    
    elif provider == 'microsoft':
        return OAuthUserInfo(
            provider=provider,
            provider_id=user_data.get('id', ''),
            email=user_data.get('mail', '') or user_data.get('userPrincipalName', ''),
            name=user_data.get('displayName', ''),
            avatar_url=None,  # Microsoft Graph API requires separate call for photo
            raw_data=user_data
        )
    
    elif provider == 'discord':
        return OAuthUserInfo(
            provider=provider,
            provider_id=user_data.get('id', ''),
            email=user_data.get('email', ''),
            name=user_data.get('username', ''),
            avatar_url=f"https://cdn.discordapp.com/avatars/{user_data.get('id')}/{user_data.get('avatar')}.png" if user_data.get('avatar') else None,
            raw_data=user_data
        )
    
    else:
        # Generic fallback
        return OAuthUserInfo(
            provider=provider,
            provider_id=str(user_data.get('id', user_data.get('sub', ''))),
            email=user_data.get('email', ''),
            name=user_data.get('name', user_data.get('login', user_data.get('username', ''))),
            avatar_url=user_data.get('avatar_url', user_data.get('picture')),
            raw_data=user_data
        )

async def create_or_update_user_from_oauth(oauth_user: OAuthUserInfo) -> UserInDB:
    """Create or update user from OAuth information"""
    user_service = await get_user_service()
    
    # Try to find existing user by email
    existing_user = await user_service.get_user_by_email(oauth_user.email)
    
    if existing_user:
        # Update existing user with OAuth info
        await user_service.update_last_login(str(existing_user.id))
        return existing_user
    else:
        # Create new user from OAuth data
        import uuid
        
        # Generate a random password (won't be used for OAuth users)
        random_password = secrets.token_urlsafe(32)
        
        # Create default tenant info
        tenant_info = TenantInfo(
            tenant_id=str(uuid.uuid4()),
            tenant_name=f"{oauth_user.name}'s Workspace",
            tenant_type="individual",
            subscription_plan="free",
            max_conversations=100,
            max_messages_per_day=1000,
            features=["basic_chat", "conversation_history", "oauth_login"]
        )
        
        user_data = UserCreate(
            name=oauth_user.name,
            email=oauth_user.email,
            phone="",  # OAuth providers might not provide phone
            password=random_password,  # Won't be used
            tenant_info=tenant_info
        )
        
        return await user_service.create_user(user_data)

def get_current_user_from_session(request: Request) -> Optional[CurrentUser]:
    """Get current user from session"""
    user_data = request.session.get('user')
    if user_data:
        # Reconstruct user from session data
        user_in_db = UserInDB(**user_data)
        return CurrentUser(user_in_db)
    return None

async def get_current_user(request: Request) -> CurrentUser:
    """Get current authenticated user from session"""
    current_user = get_current_user_from_session(request)
    if not current_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated. Please login with OAuth.",
        )
    return current_user

# Routes
@router.get("/providers", response_model=OAuthProvidersResponse)
async def get_oauth_providers(request: Request):
    """Get available OAuth providers and their login URLs"""
    oauth_config = get_oauth_config()
    providers = oauth_config.get_available_providers()
    
    base_url = str(request.base_url).rstrip('/')
    login_urls = {}
    
    for provider in providers:
        login_urls[provider] = f"{base_url}/api/auth/login/{provider}"
    
    return OAuthProvidersResponse(
        providers=providers,
        login_urls=login_urls
    )

@router.get("/login/{provider}")
async def oauth_login(provider: str, request: Request):
    """Initiate OAuth login with specified provider"""
    oauth_config = get_oauth_config()
    oauth_provider = oauth_config.get_provider(provider)
    
    if not oauth_provider:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"OAuth provider '{provider}' not configured"
        )
    
    # Generate state for CSRF protection
    state = secrets.token_urlsafe(32)
    request.session['oauth_state'] = state
    request.session['oauth_provider'] = provider
    
    # Build redirect URI
    base_url = str(request.base_url).rstrip('/')
    redirect_uri = f"{base_url}/api/auth/callback/{provider}"
    
    # Redirect to OAuth provider
    return await oauth_provider.authorize_redirect(request, redirect_uri, state=state)

@router.get("/callback/{provider}")
async def oauth_callback(provider: str, request: Request):
    """Handle OAuth callback from provider"""
    oauth_config = get_oauth_config()
    oauth_provider = oauth_config.get_provider(provider)
    
    if not oauth_provider:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"OAuth provider '{provider}' not configured"
        )
    
    # Verify state for CSRF protection
    state = request.query_params.get('state')
    session_state = request.session.get('oauth_state')
    session_provider = request.session.get('oauth_provider')
    
    if not state or state != session_state or provider != session_provider:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid OAuth state or provider mismatch"
        )
    
    try:
        # Exchange code for token
        token = await oauth_provider.authorize_access_token(request)
        
        # Get user info from provider
        if provider == 'google':
            user_data = token.get('userinfo')
            if not user_data:
                user_data = await oauth_provider.parse_id_token(request, token)
        elif provider == 'github':
            resp = await oauth_provider.get('user', token=token)
            user_data = resp.json()
            # GitHub might require separate call for email if not public
            if not user_data.get('email'):
                email_resp = await oauth_provider.get('user/emails', token=token)
                emails = email_resp.json()
                primary_email = next((e['email'] for e in emails if e['primary']), None)
                if primary_email:
                    user_data['email'] = primary_email
        elif provider == 'microsoft':
            resp = await oauth_provider.get('me', token=token)
            user_data = resp.json()
        elif provider == 'discord':
            resp = await oauth_provider.get('users/@me', token=token)
            user_data = resp.json()
        else:
            # Generic approach
            resp = await oauth_provider.get('user', token=token)
            user_data = resp.json()
        
        # Extract user information
        oauth_user = extract_user_info_from_oauth(provider, user_data)
        
        if not oauth_user.email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email not provided by OAuth provider"
            )
        
        # Create or update user
        user = await create_or_update_user_from_oauth(oauth_user)
        
        # Store user in session
        request.session['user'] = user.model_dump()
        request.session['authenticated'] = True
        request.session['auth_provider'] = provider
        
        # Clean up OAuth state
        request.session.pop('oauth_state', None)
        request.session.pop('oauth_provider', None)
        
        # Redirect to frontend success page
        frontend_url = os.getenv("FRONTEND_URL", "http://localhost:3000")
        return RedirectResponse(url=f"{frontend_url}/auth/success")
        
    except Exception as e:
        # Clean up session on error
        request.session.pop('oauth_state', None)
        request.session.pop('oauth_provider', None)
        
        # Redirect to frontend error page
        frontend_url = os.getenv("FRONTEND_URL", "http://localhost:3000")
        error_params = urlencode({"error": str(e)})
        return RedirectResponse(url=f"{frontend_url}/auth/error?{error_params}")

@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: CurrentUser = Depends(get_current_user)):
    """Get current user information"""
    return current_user.to_response()

@router.get("/check")
async def check_auth(request: Request):
    """Check if user is authenticated"""
    current_user = get_current_user_from_session(request)
    
    if not current_user:
        return {
            "authenticated": False,
            "user": None,
            "auth_provider": None
        }
    
    return {
        "authenticated": True,
        "user": current_user.to_response(),
        "auth_provider": request.session.get('auth_provider'),
        "tenant_info": {
            "tenant_id": current_user.tenant_id,
            "subscription_plan": current_user.subscription_plan,
            "max_conversations": current_user.max_conversations,
            "max_messages_per_day": current_user.max_messages_per_day,
            "conversations_count": await current_user.get_user_conversations_count(),
            "messages_today_count": await current_user.get_user_messages_today_count(),
            "can_create_conversation": await current_user.can_create_conversation(),
            "can_send_message": await current_user.can_send_message()
        }
    }

@router.post("/logout")
async def logout(request: Request):
    """Logout user by clearing session"""
    request.session.clear()
    return {"success": True, "message": "Logged out successfully"}
