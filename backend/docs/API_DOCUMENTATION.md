# Chat System API Documentation

Base URL: `http://localhost:8000`

## Authentication

This API uses **OAuth 2.0 login flow** with session-based authentication. No bearer tokens required.

### Available OAuth Providers

**GET** `/api/auth/providers`

Get list of available OAuth providers and their login URLs.

```bash
curl -X GET "http://localhost:8000/api/auth/providers"
```

**Response:**
```json
{
  "providers": ["google", "github"],
  "login_urls": {
    "google": "http://localhost:8000/api/auth/login/google",
    "github": "http://localhost:8000/api/auth/login/github"
  }
}
```

### OAuth Login

**GET** `/api/auth/login/{provider}`

Initiate OAuth login flow. Redirects to OAuth provider.

```bash
# Google OAuth
curl -X GET "http://localhost:8000/api/auth/login/google"

# GitHub OAuth  
curl -X GET "http://localhost:8000/api/auth/login/github"
```

**Response:** `302 Redirect` to OAuth provider

### OAuth Callback

**GET** `/api/auth/callback/{provider}`

OAuth callback endpoint (handled automatically by OAuth flow).

### Check Authentication Status

**GET** `/api/auth/check`

Check if user is authenticated and get user info.

```bash
curl -X GET "http://localhost:8000/api/auth/check" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt
```

**Response (Authenticated):**
```json
{
  "authenticated": true,
  "user": {
    "id": "507f1f77bcf86cd799439011",
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "",
    "is_active": true,
    "created_at": "2024-01-15T10:30:00Z",
    "last_login": "2024-01-15T14:20:00Z",
    "preferences": {}
  },
  "auth_provider": "google",
  "tenant_info": {
    "tenant_id": "tenant_123",
    "subscription_plan": "free",
    "max_conversations": 100,
    "max_messages_per_day": 1000,
    "conversations_count": 5,
    "messages_today_count": 23,
    "can_create_conversation": true,
    "can_send_message": true
  }
}
```

**Response (Not Authenticated):**
```json
{
  "authenticated": false,
  "user": null,
  "auth_provider": null
}
```

### Get Current User

**GET** `/api/auth/me`

Get current authenticated user information.

```bash
curl -X GET "http://localhost:8000/api/auth/me" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt
```

**Response:**
```json
{
  "id": "507f1f77bcf86cd799439011",
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "",
  "is_active": true,
  "created_at": "2024-01-15T10:30:00Z",
  "last_login": "2024-01-15T14:20:00Z",
  "preferences": {}
}
```

### Logout

**POST** `/api/auth/logout`

Logout user and clear session.

```bash
curl -X POST "http://localhost:8000/api/auth/logout" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt
```

**Response:**
```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

## Chat

### Send Message

**POST** `/api/chat/`

Send a chat message and get AI response.

```bash
curl -X POST "http://localhost:8000/api/chat/" \
  -H "Content-Type: application/json" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt \
  -d '{
    "message": "Hello, how are you?"
  }'
```

**Response:**
```json
{
  "response": "Hello! I'm doing well, thank you for asking. How can I help you today?",
  "session_id": "session_123",
  "conversation_id": "conv_456",
  "user_message_id": "msg_789",
  "assistant_message_id": "msg_790",
  "response_time_ms": 1250,
  "user_analytics": {
    "message_length": 18,
    "language": "en",
    "sentiment": "neutral"
  },
  "assistant_analytics": {
    "response_length": 65,
    "processing_time_ms": 1200,
    "model_used": "gemini-1.5-flash"
  }
}
```

### Send Message in Specific Conversation

**POST** `/api/chat/conversation`

Send message in a specific conversation.

```bash
curl -X POST "http://localhost:8000/api/chat/conversation" \
  -H "Content-Type: application/json" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt \
  -d '{
    "message": "What did we discuss earlier?",
    "session_id": "session_123"
  }'
```

**Response:** Same as above

### Get User Conversations

**GET** `/api/chat/conversations`

Get all conversations for the current user.

```bash
curl -X GET "http://localhost:8000/api/chat/conversations?limit=10&skip=0" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt
```

**Response:**
```json
{
  "conversations": [
    {
      "id": "conv_123",
      "session_id": "session_456",
      "title": "General Discussion",
      "conversation_type": "general",
      "status": "active",
      "created_at": "2024-01-15T10:00:00Z",
      "updated_at": "2024-01-15T14:30:00Z",
      "message_count": 8,
      "last_message_preview": "That's a great question about..."
    }
  ],
  "total": 1,
  "limit": 10,
  "skip": 0
}
```

### Create New Conversation

**POST** `/api/chat/conversations`

Create a new conversation.

```bash
curl -X POST "http://localhost:8000/api/chat/conversations" \
  -H "Content-Type: application/json" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt \
  -d '{
    "initial_message": "I want to discuss project planning"
  }'
```

**Response:**
```json
{
  "id": "conv_789",
  "session_id": "session_101112",
  "title": "Project Planning Discussion",
  "conversation_type": "general",
  "status": "active",
  "created_at": "2024-01-15T15:00:00Z",
  "message": "Conversation created successfully"
}
```

### Get Conversation Details

**GET** `/api/chat/conversations/{conversation_id}`

Get detailed information about a specific conversation.

```bash
curl -X GET "http://localhost:8000/api/chat/conversations/conv_123" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt
```

**Response:**
```json
{
  "conversation": {
    "id": "conv_123",
    "session_id": "session_456",
    "title": "General Discussion",
    "conversation_type": "general",
    "status": "active",
    "created_at": "2024-01-15T10:00:00Z",
    "updated_at": "2024-01-15T14:30:00Z",
    "message_count": 8
  },
  "recent_messages": [
    {
      "id": "msg_001",
      "content": "Hello, how are you?",
      "type": "human",
      "timestamp": "2024-01-15T10:00:00Z"
    },
    {
      "id": "msg_002",
      "content": "I'm doing well, thank you!",
      "type": "ai",
      "timestamp": "2024-01-15T10:00:05Z"
    }
  ]
}
```

### Update Conversation

**PUT** `/api/chat/conversations/{conversation_id}`

Update conversation details.

```bash
curl -X PUT "http://localhost:8000/api/chat/conversations/conv_123" \
  -H "Content-Type: application/json" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt \
  -d '{
    "title": "Updated Project Discussion",
    "conversation_type": "project"
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "Conversation updated successfully",
  "conversation": {
    "id": "conv_123",
    "title": "Updated Project Discussion",
    "conversation_type": "project",
    "updated_at": "2024-01-15T15:30:00Z"
  }
}
```

### Delete Conversation

**DELETE** `/api/chat/conversations/{conversation_id}`

Delete (archive) a conversation.

```bash
curl -X DELETE "http://localhost:8000/api/chat/conversations/conv_123" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt
```

**Response:**
```json
{
  "success": true,
  "message": "Conversation archived successfully"
}
```

### Search Messages

**GET** `/api/chat/search`

Search messages by content.

```bash
curl -X GET "http://localhost:8000/api/chat/search?query=project&session_id=session_123" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt
```

**Response:**
```json
{
  "results": [
    {
      "message_id": "msg_123",
      "content": "Let's discuss the project timeline",
      "session_id": "session_456",
      "timestamp": "2024-01-15T10:30:00Z",
      "type": "human",
      "relevance_score": 0.95
    }
  ],
  "total": 1,
  "query": "project"
}
```

## Booking

### Get Available Time Slots

**GET** `/api/booking/slots`

Get available booking time slots.

```bash
curl -X GET "http://localhost:8000/api/booking/slots?date=2024-01-20&service_type=consultation" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt
```

**Response:**
```json
{
  "slots": [
    {
      "id": "slot_123",
      "start_time": "2024-01-20T09:00:00Z",
      "end_time": "2024-01-20T10:00:00Z",
      "is_available": true,
      "service_type": "consultation",
      "duration_minutes": 60
    },
    {
      "id": "slot_124",
      "start_time": "2024-01-20T10:00:00Z",
      "end_time": "2024-01-20T11:00:00Z",
      "is_available": true,
      "service_type": "consultation",
      "duration_minutes": 60
    }
  ],
  "date": "2024-01-20",
  "total_slots": 2
}
```

### Create Booking

**POST** `/api/booking/book`

Create a new booking.

```bash
curl -X POST "http://localhost:8000/api/booking/book" \
  -H "Content-Type: application/json" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt \
  -d '{
    "slot_id": "slot_123",
    "service_type": "consultation",
    "notes": "Need help with project planning"
  }'
```

**Response:**
```json
{
  "success": true,
  "booking": {
    "id": "booking_456",
    "slot_id": "slot_123",
    "user_id": "507f1f77bcf86cd799439011",
    "service_type": "consultation",
    "start_time": "2024-01-20T09:00:00Z",
    "end_time": "2024-01-20T10:00:00Z",
    "status": "confirmed",
    "notes": "Need help with project planning",
    "created_at": "2024-01-15T15:45:00Z"
  },
  "message": "Booking created successfully"
}
```

### Get User Bookings

**GET** `/api/booking/my-bookings`

Get all bookings for the current user.

```bash
curl -X GET "http://localhost:8000/api/booking/my-bookings" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt
```

**Response:**
```json
{
  "bookings": [
    {
      "id": "booking_456",
      "slot_id": "slot_123",
      "service_type": "consultation",
      "start_time": "2024-01-20T09:00:00Z",
      "end_time": "2024-01-20T10:00:00Z",
      "status": "confirmed",
      "notes": "Need help with project planning",
      "created_at": "2024-01-15T15:45:00Z"
    }
  ],
  "total": 1
}
```

### Cancel Booking

**DELETE** `/api/booking/{booking_id}`

Cancel a booking.

```bash
curl -X DELETE "http://localhost:8000/api/booking/booking_456" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt
```

**Response:**
```json
{
  "success": true,
  "message": "Booking cancelled successfully"
}
```

## Dashboard & Analytics

### Get User Analytics

**GET** `/api/dashboard/analytics/user`

Get analytics data for the current user.

```bash
curl -X GET "http://localhost:8000/api/dashboard/analytics/user" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt
```

**Response:**
```json
{
  "user_stats": {
    "total_messages": 156,
    "total_conversations": 12,
    "messages_today": 8,
    "average_session_length": 15.5,
    "most_active_day": "Monday",
    "total_time_spent_minutes": 420
  },
  "conversation_stats": {
    "by_type": {
      "general": 8,
      "support": 3,
      "project": 1
    },
    "by_status": {
      "active": 3,
      "completed": 9
    }
  },
  "usage_trends": {
    "daily_messages": [5, 8, 12, 6, 9, 15, 8],
    "weekly_conversations": [2, 3, 1, 4, 2]
  }
}
```

### Get System Health

**GET** `/health`

Get system health status.

```bash
curl -X GET "http://localhost:8000/health"
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T16:00:00Z",
  "version": "1.0.0",
  "database_connected": true
}
```

## Error Responses

### Authentication Required

**Status:** `401 Unauthorized`

```json
{
  "detail": "Not authenticated. Please login with OAuth."
}
```

### Rate Limit Exceeded

**Status:** `429 Too Many Requests`

```json
{
  "detail": "Daily message limit reached (1000 messages per day)"
}
```

### Resource Not Found

**Status:** `404 Not Found`

```json
{
  "detail": "Conversation not found"
}
```

### Validation Error

**Status:** `422 Unprocessable Entity`

```json
{
  "detail": [
    {
      "loc": ["body", "message"],
      "msg": "field required",
      "type": "value_error.missing"
    }
  ]
}
```

### Server Error

**Status:** `500 Internal Server Error`

```json
{
  "detail": "Chat processing failed: Internal server error"
}
```

## Frontend Integration Notes

### Session Management

- Use `--cookie-jar cookies.txt --cookie cookies.txt` to maintain session
- Sessions expire after 24 hours
- Check `/api/auth/check` before making authenticated requests

### OAuth Flow

1. Redirect user to `/api/auth/login/{provider}`
2. User completes OAuth on provider site
3. User redirected to `FRONTEND_URL/auth/success` or `FRONTEND_URL/auth/error`
4. Frontend should check `/api/auth/check` to confirm authentication

### Rate Limiting

- Free tier: 1000 messages/day, 100 conversations max
- Check `tenant_info` in auth response for current limits
- Handle 429 responses gracefully

### Error Handling

- Always check response status codes
- Parse error details from response body
- Implement retry logic for 5xx errors
- Show user-friendly messages for 4xx errors

### Real-time Features

- Consider implementing WebSocket for real-time chat updates
- Poll `/api/auth/check` periodically to detect session expiry
- Cache conversation list and update incrementally
