# Chat System API Documentation

Base URL: `http://localhost:8000`

## Authentication

This API uses **OAuth 2.0 login flow** with session-based authentication. No bearer tokens required.

### Available OAuth Providers

**GET** `/api/auth/providers`

Get list of available OAuth providers and their login URLs.

```bash
curl -X GET "http://localhost:8000/api/auth/providers"
```

**Response:**
```json
{
  "providers": ["google", "github"],
  "login_urls": {
    "google": "http://localhost:8000/api/auth/login/google",
    "github": "http://localhost:8000/api/auth/login/github"
  }
}
```

### OAuth Login

**GET** `/api/auth/login/{provider}`

Initiate OAuth login flow. Redirects to OAuth provider.

```bash
# Google OAuth
curl -X GET "http://localhost:8000/api/auth/login/google"

# GitHub OAuth  
curl -X GET "http://localhost:8000/api/auth/login/github"
```

**Response:** `302 Redirect` to OAuth provider

### OAuth Callback

**GET** `/api/auth/callback/{provider}`

OAuth callback endpoint (handled automatically by OAuth flow).

### Check Authentication Status

**GET** `/api/auth/check`

Check if user is authenticated and get user info.

```bash
curl -X GET "http://localhost:8000/api/auth/check" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt
```

**Response (Authenticated):**
```json
{
  "authenticated": true,
  "user": {
    "id": "507f1f77bcf86cd799439011",
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "",
    "is_active": true,
    "created_at": "2024-01-15T10:30:00Z",
    "last_login": "2024-01-15T14:20:00Z",
    "preferences": {}
  },
  "auth_provider": "google",
  "tenant_info": {
    "tenant_id": "tenant_123",
    "subscription_plan": "free",
    "max_conversations": 100,
    "max_messages_per_day": 1000,
    "conversations_count": 5,
    "messages_today_count": 23,
    "can_create_conversation": true,
    "can_send_message": true
  }
}
```

**Response (Not Authenticated):**
```json
{
  "authenticated": false,
  "user": null,
  "auth_provider": null
}
```

### Get Current User

**GET** `/api/auth/me`

Get current authenticated user information.

```bash
curl -X GET "http://localhost:8000/api/auth/me" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt
```

**Response:**
```json
{
  "id": "507f1f77bcf86cd799439011",
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "",
  "is_active": true,
  "created_at": "2024-01-15T10:30:00Z",
  "last_login": "2024-01-15T14:20:00Z",
  "preferences": {}
}
```

### Logout

**POST** `/api/auth/logout`

Logout user and clear session.

```bash
curl -X POST "http://localhost:8000/api/auth/logout" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt
```

**Response:**
```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

## Chat

### Send Message

**POST** `/api/chat/`

Send a chat message and get AI response.

```bash
curl -X POST "http://localhost:8000/api/chat/" \
  -H "Content-Type: application/json" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt \
  -d '{
    "message": "Hello, how are you?"
  }'
```

**Response:**
```json
{
  "response": "Hello! I'm doing well, thank you for asking. How can I help you today?",
  "session_id": "session_123",
  "conversation_id": "conv_456",
  "user_message_id": "msg_789",
  "assistant_message_id": "msg_790",
  "response_time_ms": 1250,
  "user_analytics": {
    "message_length": 18,
    "language": "en",
    "sentiment": "neutral"
  },
  "assistant_analytics": {
    "response_length": 65,
    "processing_time_ms": 1200,
    "model_used": "gemini-1.5-flash"
  }
}
```

### Send Message in Specific Conversation

**POST** `/api/chat/conversation`

Send message in a specific conversation.

```bash
curl -X POST "http://localhost:8000/api/chat/conversation" \
  -H "Content-Type: application/json" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt \
  -d '{
    "message": "What did we discuss earlier?",
    "session_id": "session_123"
  }'
```

**Response:** Same as above

### Get User Conversations

**GET** `/api/chat/conversations`

Get all conversations for the current user.

```bash
curl -X GET "http://localhost:8000/api/chat/conversations?limit=10&skip=0" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt
```

**Response:**
```json
{
  "conversations": [
    {
      "id": "conv_123",
      "session_id": "session_456",
      "title": "General Discussion",
      "conversation_type": "general",
      "status": "active",
      "created_at": "2024-01-15T10:00:00Z",
      "updated_at": "2024-01-15T14:30:00Z",
      "message_count": 8,
      "last_message_preview": "That's a great question about..."
    }
  ],
  "total": 1,
  "limit": 10,
  "skip": 0
}
```

### Create New Conversation

**POST** `/api/chat/conversations`

Create a new conversation.

```bash
curl -X POST "http://localhost:8000/api/chat/conversations" \
  -H "Content-Type: application/json" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt \
  -d '{
    "initial_message": "I want to discuss project planning"
  }'
```

**Response:**
```json
{
  "id": "conv_789",
  "session_id": "session_101112",
  "title": "Project Planning Discussion",
  "conversation_type": "general",
  "status": "active",
  "created_at": "2024-01-15T15:00:00Z",
  "message": "Conversation created successfully"
}
```

### Get Conversation Details

**GET** `/api/chat/conversations/{conversation_id}`

Get detailed information about a specific conversation.

```bash
curl -X GET "http://localhost:8000/api/chat/conversations/conv_123" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt
```

**Response:**
```json
{
  "conversation": {
    "id": "conv_123",
    "session_id": "session_456",
    "title": "General Discussion",
    "conversation_type": "general",
    "status": "active",
    "created_at": "2024-01-15T10:00:00Z",
    "updated_at": "2024-01-15T14:30:00Z",
    "message_count": 8
  },
  "recent_messages": [
    {
      "id": "msg_001",
      "content": "Hello, how are you?",
      "type": "human",
      "timestamp": "2024-01-15T10:00:00Z"
    },
    {
      "id": "msg_002",
      "content": "I'm doing well, thank you!",
      "type": "ai",
      "timestamp": "2024-01-15T10:00:05Z"
    }
  ]
}
```

### Update Conversation

**PUT** `/api/chat/conversations/{conversation_id}`

Update conversation details.

```bash
curl -X PUT "http://localhost:8000/api/chat/conversations/conv_123" \
  -H "Content-Type: application/json" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt \
  -d '{
    "title": "Updated Project Discussion",
    "conversation_type": "project"
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "Conversation updated successfully",
  "conversation": {
    "id": "conv_123",
    "title": "Updated Project Discussion",
    "conversation_type": "project",
    "updated_at": "2024-01-15T15:30:00Z"
  }
}
```

### Delete Conversation

**DELETE** `/api/chat/conversations/{conversation_id}`

Delete (archive) a conversation.

```bash
curl -X DELETE "http://localhost:8000/api/chat/conversations/conv_123" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt
```

**Response:**
```json
{
  "success": true,
  "message": "Conversation archived successfully"
}
```

### Search Messages

**GET** `/api/chat/search`

Search messages by content.

```bash
curl -X GET "http://localhost:8000/api/chat/search?query=project&session_id=session_123" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt
```

**Response:**
```json
{
  "results": [
    {
      "message_id": "msg_123",
      "content": "Let's discuss the project timeline",
      "session_id": "session_456",
      "timestamp": "2024-01-15T10:30:00Z",
      "type": "human",
      "relevance_score": 0.95
    }
  ],
  "total": 1,
  "query": "project"
}
```

## Booking

### Get Available Time Slots

**GET** `/api/booking/slots`

Get available booking time slots.

```bash
curl -X GET "http://localhost:8000/api/booking/slots?date=2024-01-20&service_type=consultation" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt
```

**Response:**
```json
{
  "slots": [
    {
      "id": "slot_123",
      "start_time": "2024-01-20T09:00:00Z",
      "end_time": "2024-01-20T10:00:00Z",
      "is_available": true,
      "service_type": "consultation",
      "duration_minutes": 60
    },
    {
      "id": "slot_124",
      "start_time": "2024-01-20T10:00:00Z",
      "end_time": "2024-01-20T11:00:00Z",
      "is_available": true,
      "service_type": "consultation",
      "duration_minutes": 60
    }
  ],
  "date": "2024-01-20",
  "total_slots": 2
}
```

### Create Booking

**POST** `/api/booking/book`

Create a new booking.

```bash
curl -X POST "http://localhost:8000/api/booking/book" \
  -H "Content-Type: application/json" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt \
  -d '{
    "slot_id": "slot_123",
    "service_type": "consultation",
    "notes": "Need help with project planning"
  }'
```

**Response:**
```json
{
  "success": true,
  "booking": {
    "id": "booking_456",
    "slot_id": "slot_123",
    "user_id": "507f1f77bcf86cd799439011",
    "service_type": "consultation",
    "start_time": "2024-01-20T09:00:00Z",
    "end_time": "2024-01-20T10:00:00Z",
    "status": "confirmed",
    "notes": "Need help with project planning",
    "created_at": "2024-01-15T15:45:00Z"
  },
  "message": "Booking created successfully"
}
```

### Get User Bookings

**GET** `/api/booking/my-bookings`

Get all bookings for the current user.

```bash
curl -X GET "http://localhost:8000/api/booking/my-bookings" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt
```

**Response:**
```json
{
  "bookings": [
    {
      "id": "booking_456",
      "slot_id": "slot_123",
      "service_type": "consultation",
      "start_time": "2024-01-20T09:00:00Z",
      "end_time": "2024-01-20T10:00:00Z",
      "status": "confirmed",
      "notes": "Need help with project planning",
      "created_at": "2024-01-15T15:45:00Z"
    }
  ],
  "total": 1
}
```

### Cancel Booking

**DELETE** `/api/booking/{booking_id}`

Cancel a booking.

```bash
curl -X DELETE "http://localhost:8000/api/booking/booking_456" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt
```

**Response:**
```json
{
  "success": true,
  "message": "Booking cancelled successfully"
}
```

## Dashboard & Analytics

### Get User Analytics

**GET** `/api/dashboard/analytics/user`

Get analytics data for the current user.

```bash
curl -X GET "http://localhost:8000/api/dashboard/analytics/user" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt
```

**Response:**
```json
{
  "user_stats": {
    "total_messages": 156,
    "total_conversations": 12,
    "messages_today": 8,
    "average_session_length": 15.5,
    "most_active_day": "Monday",
    "total_time_spent_minutes": 420
  },
  "conversation_stats": {
    "by_type": {
      "general": 8,
      "support": 3,
      "project": 1
    },
    "by_status": {
      "active": 3,
      "completed": 9
    }
  },
  "usage_trends": {
    "daily_messages": [5, 8, 12, 6, 9, 15, 8],
    "weekly_conversations": [2, 3, 1, 4, 2]
  }
}
```

### Get Dashboard Overview

**GET** `/api/dashboard/overview`

Get dashboard overview with key metrics.

```bash
curl -X GET "http://localhost:8000/api/dashboard/overview" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt
```

**Response:**
```json
{
  "summary": {
    "total_users": 1250,
    "active_users_today": 89,
    "total_conversations": 5420,
    "total_messages": 28750,
    "total_bookings": 340,
    "pending_bookings": 12
  },
  "recent_activity": [
    {
      "type": "message",
      "user": "John Doe",
      "content": "Asked about project timeline",
      "timestamp": "2024-01-15T15:45:00Z"
    },
    {
      "type": "booking",
      "user": "Jane Smith",
      "content": "Booked consultation for tomorrow",
      "timestamp": "2024-01-15T15:30:00Z"
    }
  ],
  "system_status": {
    "api_health": "healthy",
    "database_status": "connected",
    "response_time_ms": 120,
    "uptime_hours": 168
  }
}
```

### Get Conversation Metrics

**GET** `/api/dashboard/metrics/conversations`

Get detailed conversation metrics.

```bash
curl -X GET "http://localhost:8000/api/dashboard/metrics/conversations?period=7d" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt
```

**Response:**
```json
{
  "period": "7d",
  "total_conversations": 89,
  "new_conversations": 23,
  "active_conversations": 45,
  "completed_conversations": 21,
  "average_messages_per_conversation": 8.5,
  "average_conversation_duration_minutes": 18.2,
  "conversation_types": {
    "general": 45,
    "support": 28,
    "booking": 12,
    "technical": 4
  },
  "daily_breakdown": [
    {
      "date": "2024-01-09",
      "new_conversations": 5,
      "total_messages": 42
    },
    {
      "date": "2024-01-10",
      "new_conversations": 8,
      "total_messages": 67
    },
    {
      "date": "2024-01-11",
      "new_conversations": 3,
      "total_messages": 28
    }
  ]
}
```

### Get User Engagement Metrics

**GET** `/api/dashboard/metrics/engagement`

Get user engagement and activity metrics.

```bash
curl -X GET "http://localhost:8000/api/dashboard/metrics/engagement?period=30d" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt
```

**Response:**
```json
{
  "period": "30d",
  "total_users": 1250,
  "active_users": 890,
  "new_users": 156,
  "returning_users": 734,
  "user_retention": {
    "day_1": 0.85,
    "day_7": 0.62,
    "day_30": 0.45
  },
  "engagement_metrics": {
    "average_session_duration_minutes": 22.5,
    "average_messages_per_user": 18.3,
    "bounce_rate": 0.12,
    "daily_active_users": 89,
    "weekly_active_users": 340,
    "monthly_active_users": 890
  },
  "top_features": [
    {
      "feature": "chat",
      "usage_count": 2840,
      "unique_users": 780
    },
    {
      "feature": "booking",
      "usage_count": 340,
      "unique_users": 290
    },
    {
      "feature": "search",
      "usage_count": 156,
      "unique_users": 120
    }
  ]
}
```

### Get Booking Metrics

**GET** `/api/dashboard/metrics/bookings`

Get booking system metrics.

```bash
curl -X GET "http://localhost:8000/api/dashboard/metrics/bookings?period=30d" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt
```

**Response:**
```json
{
  "period": "30d",
  "total_bookings": 340,
  "confirmed_bookings": 298,
  "cancelled_bookings": 42,
  "pending_bookings": 12,
  "booking_rate": 0.87,
  "cancellation_rate": 0.12,
  "average_booking_lead_time_hours": 48.5,
  "booking_by_service": {
    "consultation": 180,
    "support": 95,
    "demo": 45,
    "training": 20
  },
  "booking_by_time_slot": {
    "09:00-12:00": 145,
    "12:00-15:00": 98,
    "15:00-18:00": 97
  },
  "revenue_metrics": {
    "total_revenue": 15600.00,
    "average_booking_value": 52.34,
    "revenue_by_service": {
      "consultation": 9000.00,
      "support": 4750.00,
      "demo": 1350.00,
      "training": 500.00
    }
  }
}
```

### Get Performance Metrics

**GET** `/api/dashboard/metrics/performance`

Get system performance metrics.

```bash
curl -X GET "http://localhost:8000/api/dashboard/metrics/performance" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt
```

**Response:**
```json
{
  "api_performance": {
    "average_response_time_ms": 145,
    "p95_response_time_ms": 320,
    "p99_response_time_ms": 580,
    "requests_per_minute": 450,
    "error_rate": 0.02,
    "uptime_percentage": 99.8
  },
  "database_performance": {
    "connection_pool_usage": 0.65,
    "average_query_time_ms": 25,
    "slow_queries_count": 3,
    "database_size_mb": 2840
  },
  "ai_model_performance": {
    "average_processing_time_ms": 1250,
    "successful_responses": 2798,
    "failed_responses": 12,
    "model_accuracy_score": 0.94,
    "tokens_processed": 1250000
  },
  "resource_usage": {
    "cpu_usage_percentage": 45,
    "memory_usage_percentage": 62,
    "disk_usage_percentage": 38,
    "network_io_mbps": 12.5
  }
}
```

### Get Real-time Metrics

**GET** `/api/dashboard/metrics/realtime`

Get real-time system metrics.

```bash
curl -X GET "http://localhost:8000/api/dashboard/metrics/realtime" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt
```

**Response:**
```json
{
  "timestamp": "2024-01-15T16:30:00Z",
  "active_users": 23,
  "active_conversations": 18,
  "messages_last_hour": 156,
  "bookings_last_hour": 5,
  "system_load": {
    "cpu_percentage": 45,
    "memory_percentage": 62,
    "active_connections": 89
  },
  "api_stats": {
    "requests_last_minute": 45,
    "average_response_time_ms": 120,
    "error_count_last_hour": 2
  },
  "queue_status": {
    "pending_messages": 0,
    "processing_messages": 3,
    "failed_messages": 0
  }
}
```

### Export Analytics Data

**GET** `/api/dashboard/export`

Export analytics data in various formats.

```bash
# Export as JSON
curl -X GET "http://localhost:8000/api/dashboard/export?format=json&period=30d&type=conversations" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt

# Export as CSV
curl -X GET "http://localhost:8000/api/dashboard/export?format=csv&period=30d&type=users" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt
```

**Response (JSON):**
```json
{
  "export_id": "export_123",
  "format": "json",
  "type": "conversations",
  "period": "30d",
  "download_url": "http://localhost:8000/api/dashboard/download/export_123",
  "expires_at": "2024-01-16T16:30:00Z",
  "file_size_bytes": 2048576
}
```

### Download Export

**GET** `/api/dashboard/download/{export_id}`

Download exported analytics data.

```bash
curl -X GET "http://localhost:8000/api/dashboard/download/export_123" \
  --cookie-jar cookies.txt \
  --cookie cookies.txt \
  -o analytics_export.json
```

**Response:** File download

### Get System Health

**GET** `/health`

Get system health status.

```bash
curl -X GET "http://localhost:8000/health"
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T16:00:00Z",
  "version": "1.0.0",
  "database_connected": true
}
```

## Error Responses

### Authentication Required

**Status:** `401 Unauthorized`

```json
{
  "detail": "Not authenticated. Please login with OAuth."
}
```

### Rate Limit Exceeded

**Status:** `429 Too Many Requests`

```json
{
  "detail": "Daily message limit reached (1000 messages per day)"
}
```

### Resource Not Found

**Status:** `404 Not Found`

```json
{
  "detail": "Conversation not found"
}
```

### Validation Error

**Status:** `422 Unprocessable Entity`

```json
{
  "detail": [
    {
      "loc": ["body", "message"],
      "msg": "field required",
      "type": "value_error.missing"
    }
  ]
}
```

### Server Error

**Status:** `500 Internal Server Error`

```json
{
  "detail": "Chat processing failed: Internal server error"
}
```

## Frontend Integration Notes

### Session Management

- Use `--cookie-jar cookies.txt --cookie cookies.txt` to maintain session
- Sessions expire after 24 hours
- Check `/api/auth/check` before making authenticated requests

### OAuth Flow

1. Redirect user to `/api/auth/login/{provider}`
2. User completes OAuth on provider site
3. User redirected to `FRONTEND_URL/auth/success` or `FRONTEND_URL/auth/error`
4. Frontend should check `/api/auth/check` to confirm authentication

### Rate Limiting

- Free tier: 1000 messages/day, 100 conversations max
- Check `tenant_info` in auth response for current limits
- Handle 429 responses gracefully

### Error Handling

- Always check response status codes
- Parse error details from response body
- Implement retry logic for 5xx errors
- Show user-friendly messages for 4xx errors

### Real-time Features

- Consider implementing WebSocket for real-time chat updates
- Poll `/api/auth/check` periodically to detect session expiry
- Cache conversation list and update incrementally
